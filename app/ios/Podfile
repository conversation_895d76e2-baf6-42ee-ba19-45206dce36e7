require_relative '../../node_modules/react-native/scripts/react_native_pods'

require_relative '../../node_modules/@react-native-community/cli-platform-ios/native_modules'

# Resolve react_native_pods.rb with node to allow for hoisting
require Pod::Executable.execute_command('node', ['-p',
  'require.resolve(
    "react-native/scripts/react_native_pods.rb",
    {paths: [process.argv[1]]},
  )', __dir__]).strip
#minimum sdk version upgraded to support Airship
platform :ios, "15.0"
prepare_react_native_project!



# If you are using a `react-native-flipper` your iOS build will fail when `NO_FLIPPER=1` is set.
# because `react-native-flipper` depends on (FlipperKit,...) that will be excluded
#
# To fix this you can also exclude `react-native-flipper` using a `react-native.config.js`
# ```js
# module.exports = {
#   dependencies: {
#     ...(process.env.NO_FLIPPER ? { 'react-native-flipper': { platforms: { ios: null } } } : {}),
# ```
flipper_config = ENV['NO_FLIPPER'] == "1" ? FlipperConfiguration.disabled : FlipperConfiguration.enabled

linkage = ENV['USE_FRAMEWORKS']
if linkage != nil
  Pod::UI.puts "Configuring Pod with #{linkage}ally linked Frameworks".green
  use_frameworks! :linkage => linkage.to_sym
end

target 'bppulse' do
  # React Native Maps
  pod 'react-native-google-maps', :path => '../../node_modules/react-native-maps'



  # Cardinal SDK
  pod 'cardinal-mobile-sdk', :git => 'https://dev.azure.com/bp-digital/DCM_Frameworks/_git/cardinal-mobile-sdk'

  # Firebase
  pod 'Firebase', :modular_headers => true
  pod 'GoogleUtilities', :modular_headers => true
  pod 'FirebaseCore', :modular_headers => true
  pod 'FirebaseInstallations', :modular_headers => true
  pod 'FirebaseABTesting', :modular_headers => true
  pod 'GoogleDataTransport', :modular_headers => true
  pod 'FirebaseCoreExtension', :modular_headers => true
  pod 'nanopb', :modular_headers => true

  config = use_native_modules!

  # Flags change depending on the env values.
  flags = get_default_flags()

  use_react_native!(
    :path => config[:reactNativePath],
    # Hermes is now enabled by default. Disable by setting this flag to false.
    :hermes_enabled => flags[:hermes_enabled],
    :fabric_enabled => flags[:fabric_enabled],
    # Enables Flipper.
    #
    # Note that if you have use_frameworks! enabled, Flipper will not work and
    # you should disable the next line.
    #:flipper_configuration => flipper_config,
    # An absolute path to your application root.
    :app_path => "#{Pod::Config.instance.installation_root}/.."
  )

  target 'bppulseTests' do
    inherit! :complete
    # Pods for testing
  end

  target 'aralpulse' do
    inherit! :complete
    # Pods for testing
  end

  target 'bpUSpulse' do
    inherit! :complete
    # Pods for testing
  end

  target "NotificationServiceExtension" do
    pod 'AirshipServiceExtension'
  end

  post_install do |installer|
    # https://github.com/facebook/react-native/blob/main/packages/react-native/scripts/react_native_pods.rb#L197-L202
    react_native_post_install(
      installer,
      config[:reactNativePath],
      :mac_catalyst_enabled => false
    )
    __apply_Xcode_12_5_M1_post_install_workaround(installer)

    # Enforce iOS 15.0 on all pods + fix Swift optimization levels
    installer.pods_project.targets.each do |target|
      target.build_configurations.each do |build_config|
        # Force all pods to use iOS 15.0 to remove the “set to 11.0” warnings
        build_config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '15.0'

        # Fix the “Disabling previews” note by setting -Onone in Debug
        if build_config.name == 'Debug'
          build_config.build_settings['SWIFT_OPTIMIZATION_LEVEL'] = '-Onone'
        end

        # Example of excluding simulator arm64 if needed
        build_config.build_settings["EXCLUDED_ARCHS[sdk=iphonesimulator*]"] = "arm64"
        build_config.build_settings['GCC_PREPROCESSOR_DEFINITIONS'] ||= [
          '$(inherited)',
          '_LIBCPP_ENABLE_CXX17_REMOVED_UNARY_BINARY_FUNCTION'
        ]
      end
    end

    # Example: Stripping bitcode from Hermes frameworks
    bitcode_strip_path = `xcrun --find bitcode_strip`.chomp
    def strip_bitcode_from_framework(bitcode_strip_path, framework_relative_path)
      framework_path = File.join(Dir.pwd, framework_relative_path)
      command = "#{bitcode_strip_path} #{framework_path} -r -o #{framework_path}"
      puts "Stripping bitcode: #{command}"
      system(command)
    end

    framework_paths = [
      "Pods/hermes-engine/destroot/Library/Frameworks/macosx/hermes.framework/hermes",
      "Pods/hermes-engine/destroot/Library/Frameworks/macosx/hermes.framework/Versions/Current/hermes",
      "Pods/hermes-engine/destroot/Library/Frameworks/universal/hermes.xcframework/ios-arm64/hermes.framework/hermes",
      "Pods/hermes-engine/destroot/Library/Frameworks/universal/hermes.xcframework/ios-arm64_x86_64-maccatalyst/hermes.framework/hermes"
    ]

    framework_paths.each do |framework_relative_path|
      strip_bitcode_from_framework(bitcode_strip_path, framework_relative_path)
    end
  end
end
